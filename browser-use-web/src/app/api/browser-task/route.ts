import { NextRequest, NextResponse } from 'next/server'
import { v4 as uuidv4 } from 'uuid'
import { tasks, setTask } from '@/lib/task-storage'

interface BrowserTask {
  id: string
  task: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  messages: Message[]
  createdAt: Date
  duration?: number
}

interface Message {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: Date
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const { task, model } = await request.json()

    if (!task || typeof task !== 'string') {
      return NextResponse.json(
        { error: 'Task is required' },
        { status: 400 }
      )
    }

    const taskId = uuidv4()
    const newTask: BrowserTask = {
      id: taskId,
      task,
      status: 'pending',
      messages: [
        {
          id: uuidv4(),
          type: 'system',
          content: `Starting browser task: ${task}`,
          timestamp: new Date(),
        }
      ],
      createdAt: new Date(),
    }

    setTask(taskId, newTask)

    // Start the browser task in the background
    startBrowserTask(taskId, task, model)

    return NextResponse.json({ task: newTask })
  } catch (error) {
    console.error('Error creating browser task:', error)
    return NextResponse.json(
      { error: 'Failed to create browser task' },
      { status: 500 }
    )
  }
}

async function startBrowserTask(taskId: string, task: string, model: string) {
  const taskData = tasks.get(taskId)
  if (!taskData) return

  // Update status to running
  taskData.status = 'running'
  taskData.messages.push({
    id: uuidv4(),
    type: 'assistant',
    content: 'Starting browser automation...',
    timestamp: new Date(),
  })

  try {
    // In a real implementation, this would connect to the browser-use backend
    // For now, we'll simulate the browser task
    
    // Simulate browser actions
    const actions = [
      'Navigating to search engine...',
      'Searching for relevant information...',
      'Analyzing search results...',
      'Extracting key information...',
      'Compiling final response...'
    ]

    for (const action of actions) {
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      taskData.messages.push({
        id: uuidv4(),
        type: 'system',
        content: action,
        timestamp: new Date(),
        metadata: {
          action: action,
          url: 'https://example.com'
        }
      })
    }

    // Add final response
    await new Promise(resolve => setTimeout(resolve, 1000))
    taskData.messages.push({
      id: uuidv4(),
      type: 'assistant',
      content: `I've completed the task: ${task}. Here's what I found: [Simulated response based on task "${task}"]`,
      timestamp: new Date(),
      metadata: {
        url: 'https://example.com/results'
      }
    })

    taskData.status = 'completed'
    taskData.duration = Date.now() - taskData.createdAt.getTime()

  } catch (error) {
    console.error('Error in browser task:', error)
    taskData.status = 'failed'
    taskData.messages.push({
      id: uuidv4(),
      type: 'assistant',
      content: 'Sorry, I encountered an error while completing this task.',
      timestamp: new Date(),
    })
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const taskId = searchParams.get('taskId')

  if (!taskId) {
    return NextResponse.json(
      { error: 'Task ID is required' },
      { status: 400 }
    )
  }

  const task = tasks.get(taskId)
  if (!task) {
    return NextResponse.json(
      { error: 'Task not found' },
      { status: 404 }
    )
  }

  return NextResponse.json(task)
}

export async function DELETE(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const taskId = searchParams.get('taskId')

  if (!taskId) {
    return NextResponse.json(
      { error: 'Task ID is required' },
      { status: 400 }
    )
  }

  const task = tasks.get(taskId)
  if (!task) {
    return NextResponse.json(
      { error: 'Task not found' },
      { status: 404 }
    )
  }

  try {
    // In a real implementation, this would stop the browser task
    // For now, we'll just remove the task from storage
    tasks.delete(taskId)
    return NextResponse.json({ message: 'Task deleted successfully' })
  } catch (error) {
    console.error("Failed to stop task:", error)
    return NextResponse.json(
      { error: 'Failed to stop task' },
      { status: 500 }
    )
  }
}
