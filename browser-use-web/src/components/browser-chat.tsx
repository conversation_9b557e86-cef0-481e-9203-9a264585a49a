"use client"

import { useState, useRef, useEffect } from "react"
import { Send, Bot, User, Globe, Loader2, Square, Monitor, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Toaster } from "@/components/ui/sonner"
import { toast } from "sonner"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

interface Message {
  id: string
  type: "user" | "assistant" | "system"
  content: string
  timestamp: Date
  metadata?: {
    url?: string
    action?: string
    screenshot?: string
  }
}

interface BrowserTask {
  id: string
  task: string
  status: "pending" | "running" | "completed" | "failed"
  messages: Message[]
  createdAt: Date
  duration?: number
}

export function BrowserChat() {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [isRunning, setIsRunning] = useState(false)
  const [currentTask, setCurrentTask] = useState<BrowserTask | null>(null)
  const [activeTab, setActiveTab] = useState("chat")
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight
    }
  }, [messages])

  const sendMessage = async () => {
    if (!input.trim() || isRunning) return

    const userMessage: Message = {
      id: Date.now().toString(),
      type: "user",
      content: input,
      timestamp: new Date(),
    }

    setMessages(prev => [...prev, userMessage])
    setInput("")
    setIsRunning(true)

    try {
      const response = await fetch("/api/browser-task", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          task: input,
          model: "gemini-2.5-flash",
        }),
      })

      if (!response.ok) {
        throw new Error("Failed to start browser task")
      }

      const data = await response.json()
      setCurrentTask(data.task)

      pollTaskStatus(data.task.id)
    } catch (error) {
      console.error("Failed to start browser task:", error)
      toast.error("Failed to start browser task")
      setIsRunning(false)
    }
  }

  const pollTaskStatus = async (taskId: string) => {
    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/api/browser-task/${taskId}`)
        const data = await response.json()

        if (data.messages && data.messages.length > messages.length) {
          setMessages(data.messages)
        }

        if (data.status === "completed" || data.status === "failed") {
          setIsRunning(false)
          setCurrentTask(data)
          clearInterval(interval)
          
          if (data.status === "completed") {
            toast.success("Task completed successfully!")
          } else {
            toast.error("Task failed to complete")
          }
        }
      } catch (_error) {
        console.error("Error polling task status:", _error)
        setIsRunning(false)
        clearInterval(interval)
        toast.error("Error checking task status")
      }
    }, 2000)

    return () => clearInterval(interval)
  }

  const stopTask = async () => {
    if (!currentTask) return

    try {
      await fetch(`/api/browser-task/${currentTask.id}/stop`, {
        method: "POST",
      })
      setIsRunning(false)
      toast.success("Browser task has been stopped")
    } catch (error) {
      console.error("Failed to stop task:", error)
      toast.error("Failed to stop task")
    }
  }

  return (
    <>
      <Toaster />
      <div className="flex flex-col h-full max-w-4xl mx-auto p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="browser">Browser View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="chat" className="mt-0">
            <Card className="flex-1 flex flex-col h-[600px] border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-sm">
              <div className="p-4 border-b">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Globe className="h-5 w-5 text-blue-500" />
                    <h2 className="text-lg font-semibold">Browser AI Assistant</h2>
                  </div>
                  <div className="flex items-center gap-2">
                    {isRunning && (
                      <>
                        <Badge variant="secondary" className="animate-pulse">
                          <Loader2 className="h-3 w-3 animate-spin mr-1" />
                          Running
                        </Badge>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={stopTask}
                          className="h-8"
                        >
                          <Square className="h-3 w-3 mr-1" />
                          Stop
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>

              <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
                <div className="space-y-4">
                    {messages.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <Globe className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <p className="text-lg font-medium">Ask me to do anything on the web...</p>
                        <p className="text-sm mt-2 text-muted-foreground">
                          Examples: &quot;Find the latest news about AI&quot;, &quot;Book a flight to Paris&quot;, &quot;Check my email&quot;
                        </p>
                      </div>
                    ) : (
                      messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex gap-3 ${message.type === "user" ? "justify-end" : "justify-start"}`}
                      >
                        {message.type !== "user" && (
                          <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                            {message.type === "assistant" ? (
                              <Bot className="h-4 w-4" />
                            ) : (
                              <Monitor className="h-4 w-4" />
                            )}
                          </div>
                        )}
                        <div
                          className={`max-w-[70%] rounded-lg px-4 py-2 ${
                            message.type === "user"
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted"
                          }`}
                        >
                          <p className="text-sm">{message.content}</p>
                          {message.metadata?.url && (
                            <p className="text-xs opacity-70 mt-1">
                              <a href={message.metadata.url} target="_blank" rel="noopener noreferrer">
                                {message.metadata.url}
                              </a>
                            </p>
                          )}
                          <p className="text-xs opacity-50 mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </p>
                        </div>
                        {message.type === "user" && (
                          <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
                            <User className="h-4 w-4" />
                          </div>
                        )}
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>

              <div className="p-4 border-t">
                <div className="flex gap-2">
                  <Textarea
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !e.shiftKey) {
                        e.preventDefault()
                        sendMessage()
                      }
                    }}
                    placeholder="Ask me to do something on the web..."
                    className="min-h-[40px] max-h-[120px]"
                    disabled={isRunning}
                  />
                  <Button
                    size="icon"
                    onClick={sendMessage}
                    disabled={!input.trim() || isRunning}
                    className="h-10 w-10"
                  >
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="browser" className="mt-0">
            <Card className="h-[600px] flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 shadow-sm">
              <div className="text-center text-muted-foreground">
                <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Browser View</p>
                <p className="text-sm mt-2">Live browser screenshots will appear here</p>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </>
  )
}
